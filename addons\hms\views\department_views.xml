<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View for Department -->
    <record id="view_department_tree" model="ir.ui.view">
        <field name="name">hms.department.tree</field>
        <field name="model">hms.department</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code"/>
                <field name="name"/>
                <field name="head_of_department"/>
                <field name="location"/>
                <field name="capacity"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Form View for Department -->
    <record id="view_department_form" model="ir.ui.view">
        <field name="name">hms.department.form</field>
        <field name="model">hms.department</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Department Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="head_of_department"/>
                            <field name="location"/>
                            <field name="capacity"/>
                        </group>
                        <group>
                            <field name="phone"/>
                            <field name="email" widget="email"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Department description..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View for Department -->
    <record id="view_department_search" model="ir.ui.view">
        <field name="name">hms.department.search</field>
        <field name="model">hms.department</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="head_of_department"/>
                <field name="location"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Location" name="group_location" context="{'group_by': 'location'}"/>
                    <filter string="Head of Department" name="group_head" context="{'group_by': 'head_of_department'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action for Department -->
    <record id="action_department" model="ir.actions.act_window">
        <field name="name">Departments</field>
        <field name="res_model">hms.department</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_department_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first department!
            </p>
            <p>
                Manage hospital departments including their details, head of department, and location.
            </p>
        </field>
    </record>
</odoo>
