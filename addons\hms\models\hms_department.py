# -*- coding: utf-8 -*-

from odoo import models, fields, api


class HMSDepartment(models.Model):
    _name = 'hms.department'
    _description = 'Hospital Department'
    _rec_name = 'name'

    name = fields.Char(string='Department Name', required=True)
    code = fields.Char(string='Department Code', required=True)
    description = fields.Text(string='Description')
    head_of_department = fields.Char(string='Head of Department')
    phone = fields.Char(string='Phone Number')
    email = fields.Char(string='Email')
    location = fields.Char(string='Location/Floor')
    capacity = fields.Integer(string='Capacity')
    active = fields.Boolean(string='Active', default=True)
    
    # Computed field for display name
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    
    @api.depends('name', 'code')
    def _compute_display_name(self):
        for record in self:
            if record.code and record.name:
                record.display_name = f"[{record.code}] {record.name}"
            else:
                record.display_name = record.name or record.code or ''
    
    # Constraint to ensure unique department code
    _sql_constraints = [
        ('unique_code', 'UNIQUE(code)', 'Department code must be unique!'),
    ]
