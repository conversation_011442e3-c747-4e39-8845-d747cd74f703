<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo data for departments -->
        <record id="department_cardiology" model="hms.department">
            <field name="name">Cardiology</field>
            <field name="code">CARD</field>
            <field name="description">Department specializing in heart and cardiovascular diseases</field>
            <field name="head_of_department">Dr. <PERSON></field>
            <field name="phone">+20-123-456-789</field>
            <field name="email"><EMAIL></field>
            <field name="location">Floor 3, Wing A</field>
            <field name="capacity">50</field>
            <field name="active">True</field>
        </record>

        <record id="department_emergency" model="hms.department">
            <field name="name">Emergency</field>
            <field name="code">ER</field>
            <field name="description">Emergency and trauma care department</field>
            <field name="head_of_department">Dr. <PERSON><PERSON></field>
            <field name="phone">+20-123-456-790</field>
            <field name="email"><EMAIL></field>
            <field name="location">Ground Floor</field>
            <field name="capacity">30</field>
            <field name="active">True</field>
        </record>

        <record id="department_pediatrics" model="hms.department">
            <field name="name">Pediatrics</field>
            <field name="code">PED</field>
            <field name="description">Children's healthcare department</field>
            <field name="head_of_department">Dr. <PERSON> <PERSON>ah</field>
            <field name="phone">+20-123-456-791</field>
            <field name="email"><EMAIL></field>
            <field name="location">Floor 2, Wing B</field>
            <field name="capacity">40</field>
            <field name="active">True</field>
        </record>

        <record id="department_surgery" model="hms.department">
            <field name="name">Surgery</field>
            <field name="code">SURG</field>
            <field name="description">General and specialized surgery department</field>
            <field name="head_of_department">Dr. Nadia Ibrahim</field>
            <field name="phone">+20-123-456-792</field>
            <field name="email"><EMAIL></field>
            <field name="location">Floor 4, Wing A</field>
            <field name="capacity">25</field>
            <field name="active">True</field>
        </record>

        <record id="department_radiology" model="hms.department">
            <field name="name">Radiology</field>
            <field name="code">RAD</field>
            <field name="description">Medical imaging and diagnostic department</field>
            <field name="head_of_department">Dr. Omar Khaled</field>
            <field name="phone">+20-123-456-793</field>
            <field name="email"><EMAIL></field>
            <field name="location">Basement Level 1</field>
            <field name="capacity">15</field>
            <field name="active">True</field>
        </record>
    </data>
</odoo>
